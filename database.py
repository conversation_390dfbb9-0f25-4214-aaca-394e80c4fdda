import sqlite3
import os
from datetime import datetime
import pandas as pd

class DatabaseManager:
    def __init__(self, db_path="data/cashier.db"):
        self.db_path = db_path
        self.ensure_data_directory()
        self.init_database()
    
    def ensure_data_directory(self):
        """إنشاء مجلد البيانات إذا لم يكن موجوداً"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    def init_database(self):
        """إنشاء الجداول الأساسية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                barcode TEXT UNIQUE,
                price REAL NOT NULL,
                category TEXT,
                stock_quantity INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                total_amount REAL NOT NULL,
                payment_method TEXT NOT NULL,
                sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                cashier_name TEXT
            )
        ''')
        
        # جدول تفاصيل المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER,
                product_id INTEGER,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # جدول الفئات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        return sqlite3.connect(self.db_path)
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام وإرجاع النتائج"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        results = cursor.fetchall()
        conn.commit()
        conn.close()
        return results
    
    def execute_insert(self, query, params):
        """تنفيذ استعلام إدراج وإرجاع ID الصف الجديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        last_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return last_id
    
    def backup_to_excel(self, backup_path="data/backup.xlsx"):
        """إنشاء نسخة احتياطية في ملف Excel"""
        conn = self.get_connection()
        
        # قراءة جميع الجداول
        products_df = pd.read_sql_query("SELECT * FROM products", conn)
        sales_df = pd.read_sql_query("SELECT * FROM sales", conn)
        sale_items_df = pd.read_sql_query("SELECT * FROM sale_items", conn)
        categories_df = pd.read_sql_query("SELECT * FROM categories", conn)
        
        # حفظ في ملف Excel
        with pd.ExcelWriter(backup_path, engine='openpyxl') as writer:
            products_df.to_excel(writer, sheet_name='المنتجات', index=False)
            sales_df.to_excel(writer, sheet_name='المبيعات', index=False)
            sale_items_df.to_excel(writer, sheet_name='تفاصيل_المبيعات', index=False)
            categories_df.to_excel(writer, sheet_name='الفئات', index=False)
        
        conn.close()
        return backup_path
    
    def get_daily_sales_summary(self, date=None):
        """الحصول على ملخص المبيعات اليومية"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        query = '''
            SELECT 
                COUNT(*) as total_transactions,
                SUM(total_amount) as total_sales,
                AVG(total_amount) as average_sale,
                payment_method,
                COUNT(*) as payment_count
            FROM sales 
            WHERE DATE(sale_date) = ?
            GROUP BY payment_method
        '''
        
        return self.execute_query(query, (date,))

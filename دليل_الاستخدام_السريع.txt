===============================================
        دليل الاستخدام السريع
        نظام الكاشير المتقدم
===============================================

🚀 تشغيل البرنامج:
------------------
1. تشغيل مباشر: python main.py
2. تشغيل مع فحص: python run.py

📦 إعداد البرنامج لأول مرة:
---------------------------
python setup.py

🛍️ كيفية استخدام نقطة البيع:
-----------------------------
1. في الشاشة الرئيسية، ابحث عن المنتج في الجدول الأيسر
2. انقر مرتين على المنتج لإضافته للسلة
3. أدخل الكمية المطلوبة
4. كرر العملية لإضافة منتجات أخرى
5. اختر طريقة الدفع (نقدي/كارت/محفظة إلكترونية)
6. أدخل المبلغ المدفوع بالدينار العراقي (للدفع النقدي)
7. اضغط "إتمام البيع"

📋 إدارة المنتجات:
------------------
• اذهب إلى قائمة "المنتجات" > "إدارة المنتجات"
• إضافة منتج: اضغط "إضافة منتج" وأدخل البيانات
• تعديل منتج: اختر المنتج واضغط "تعديل منتج"
• حذف منتج: اختر المنتج واضغط "حذف منتج"

📊 التقارير:
------------
• اذهب إلى قائمة "التقارير"
• تقرير يومي: ملخص مبيعات اليوم
• تقرير المخزون: حالة المخزون والمنتجات منخفضة المخزون
• أداء المنتجات: أكثر المنتجات مبيعاً

💾 النسخ الاحتياطية:
-------------------
• قائمة "ملف" > "نسخة احتياطية"
• يتم حفظ النسخة في مجلد data/backup.xlsx

🔧 حل المشاكل الشائعة:
---------------------
• إذا لم يعمل البرنامج: تأكد من تثبيت Python 3.7+
• خطأ في المكتبات: شغل python setup.py
• مشكلة في قاعدة البيانات: احذف ملف data/cashier.db

📁 هيكل الملفات:
----------------
main.py              - الملف الرئيسي
database.py          - إدارة قاعدة البيانات
product_manager.py   - إدارة المنتجات
pos_system.py        - نظام نقطة البيع
invoice_manager.py   - إدارة الفواتير
reports.py           - التقارير
setup.py             - إعداد البرنامج
run.py               - تشغيل مع فحص
data/                - مجلد البيانات
  ├── cashier.db     - قاعدة البيانات
  ├── invoices/      - الفواتير
  ├── reports/       - التقارير
  └── barcodes/      - صور الباركود

⚡ اختصارات مفيدة:
------------------
• Enter في حقل البحث: بحث فوري
• النقر المزدوج على منتج: إضافة للسلة
• Escape: إلغاء النوافذ المنبثقة

📞 الدعم:
----------
• راجع ملف README.md للتفاصيل الكاملة
• تحقق من رسائل الخطأ في وحدة التحكم
• تأكد من تثبيت جميع المكتبات المطلوبة

===============================================
        تم تطوير البرنامج بـ Python
        جميع الحقوق محفوظة
===============================================

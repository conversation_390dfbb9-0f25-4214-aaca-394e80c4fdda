#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعداد وتثبيت برنامج الكاشير
"""

import subprocess
import sys
import os

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    print("جاري تثبيت المكتبات المطلوبة...")

    requirements = [
        'pandas',
        'openpyxl',
        'reportlab',
        'Pillow',
        'pyzbar',
        'opencv-python',
        'python-barcode',
        'qrcode',
        'matplotlib'
    ]

    for package in requirements:
        try:
            print(f"تثبيت {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"تم تثبيت {package} بنجاح")
        except subprocess.CalledProcessError:
            print(f"فشل في تثبيت {package}")
            return False

    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'data',
        'data/invoices',
        'data/reports',
        'data/barcodes'
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"تم إنشاء مجلد: {directory}")

def add_sample_data():
    """إضافة بيانات تجريبية"""
    try:
        from product_manager import ProductManager

        pm = ProductManager()

        # إضافة فئات
        pm.add_category("طعام", "منتجات غذائية")
        pm.add_category("مشروبات", "مشروبات متنوعة")
        pm.add_category("أدوات", "أدوات منزلية")

        # إضافة منتجات تجريبية (بالدينار العراقي)
        sample_products = [
            ("خبز أبيض", 1000, "طعام", None, 50),
            ("حليب طازج", 3000, "مشروبات", None, 30),
            ("أرز بسمتي", 8000, "طعام", None, 25),
            ("ماء معدني", 500, "مشروبات", None, 100),
            ("صابون", 2500, "أدوات", None, 40),
            ("شاي أحمر", 5000, "مشروبات", None, 20),
            ("سكر أبيض", 3500, "طعام", None, 35),
            ("زيت طبخ", 12000, "طعام", None, 15),
            ("منظف أطباق", 4000, "أدوات", None, 25),
            ("عصير برتقال", 2000, "مشروبات", None, 60)
        ]

        for product in sample_products:
            pm.add_product(*product)

        print("تم إضافة البيانات التجريبية بنجاح")

    except Exception as e:
        print(f"خطأ في إضافة البيانات التجريبية: {e}")

def main():
    """الدالة الرئيسية للإعداد"""
    print("=== إعداد برنامج الكاشير المتقدم ===")
    print()

    # تثبيت المكتبات
    if not install_requirements():
        print("فشل في تثبيت بعض المكتبات. يرجى المحاولة مرة أخرى.")
        return

    print()
    print("تم تثبيت جميع المكتبات بنجاح!")
    print()

    # إنشاء المجلدات
    create_directories()
    print()

    # إضافة بيانات تجريبية
    response = input("هل تريد إضافة بيانات تجريبية؟ (y/n): ")
    if response.lower() in ['y', 'yes', 'نعم']:
        add_sample_data()

    print()
    print("=== تم الإعداد بنجاح! ===")
    print("يمكنك الآن تشغيل البرنامج باستخدام: python main.py")

if __name__ == "__main__":
    main()

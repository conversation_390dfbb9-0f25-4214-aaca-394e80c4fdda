#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تحديث العملة من الريال إلى الدينار العراقي
"""

import sqlite3
import os

def update_currency():
    """تحديث أسعار المنتجات لتناسب الدينار العراقي"""
    
    db_path = "data/cashier.db"
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة. يرجى تشغيل البرنامج أولاً.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # معدل التحويل التقريبي من الريال السعودي إلى الدينار العراقي
        # 1 ريال سعودي ≈ 400 دينار عراقي
        conversion_rate = 400
        
        print("جاري تحديث أسعار المنتجات...")
        
        # الحصول على جميع المنتجات
        cursor.execute("SELECT id, name, price FROM products")
        products = cursor.fetchall()
        
        updated_count = 0
        
        for product in products:
            product_id, name, old_price = product
            
            # تحويل السعر إلى الدينار العراقي
            new_price = int(old_price * conversion_rate)
            
            # تحديث السعر في قاعدة البيانات
            cursor.execute("UPDATE products SET price = ? WHERE id = ?", (new_price, product_id))
            
            print(f"تم تحديث {name}: من {old_price} ريال إلى {new_price} دينار عراقي")
            updated_count += 1
        
        # تحديث أسعار المبيعات السابقة
        print("\nجاري تحديث المبيعات السابقة...")
        
        # تحديث إجمالي المبيعات
        cursor.execute("UPDATE sales SET total_amount = total_amount * ?", (conversion_rate,))
        
        # تحديث أسعار عناصر المبيعات
        cursor.execute("UPDATE sale_items SET unit_price = unit_price * ?, total_price = total_price * ?", 
                      (conversion_rate, conversion_rate))
        
        # حفظ التغييرات
        conn.commit()
        
        print(f"\n✅ تم تحديث {updated_count} منتج بنجاح!")
        print("✅ تم تحديث المبيعات السابقة!")
        print("✅ تم تحويل العملة إلى الدينار العراقي بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث العملة: {e}")
        conn.rollback()
    
    finally:
        conn.close()

def reset_sample_data():
    """إعادة تعيين البيانات التجريبية بالدينار العراقي"""
    
    db_path = "data/cashier.db"
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("جاري إعادة تعيين البيانات التجريبية...")
        
        # حذف البيانات الموجودة
        cursor.execute("DELETE FROM sale_items")
        cursor.execute("DELETE FROM sales")
        cursor.execute("DELETE FROM products")
        cursor.execute("DELETE FROM categories")
        
        # إضافة الفئات
        categories = [
            ("طعام", "منتجات غذائية"),
            ("مشروبات", "مشروبات متنوعة"),
            ("أدوات", "أدوات منزلية")
        ]
        
        for category in categories:
            cursor.execute("INSERT INTO categories (name, description) VALUES (?, ?)", category)
        
        # إضافة المنتجات بأسعار الدينار العراقي
        products = [
            ("خبز أبيض", "PRD00001", 1000, "طعام", 50),
            ("حليب طازج", "PRD00002", 3000, "مشروبات", 30),
            ("أرز بسمتي", "PRD00003", 8000, "طعام", 25),
            ("ماء معدني", "PRD00004", 500, "مشروبات", 100),
            ("صابون", "PRD00005", 2500, "أدوات", 40),
            ("شاي أحمر", "PRD00006", 5000, "مشروبات", 20),
            ("سكر أبيض", "PRD00007", 3500, "طعام", 35),
            ("زيت طبخ", "PRD00008", 12000, "طعام", 15),
            ("منظف أطباق", "PRD00009", 4000, "أدوات", 25),
            ("عصير برتقال", "PRD00010", 2000, "مشروبات", 60)
        ]
        
        for product in products:
            cursor.execute("""
                INSERT INTO products (name, barcode, price, category, stock_quantity)
                VALUES (?, ?, ?, ?, ?)
            """, product)
        
        conn.commit()
        print("✅ تم إعادة تعيين البيانات التجريبية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إعادة تعيين البيانات: {e}")
        conn.rollback()
    
    finally:
        conn.close()

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("        تحديث العملة إلى الدينار العراقي")
    print("=" * 50)
    print()
    
    print("اختر العملية المطلوبة:")
    print("1. تحديث الأسعار الموجودة")
    print("2. إعادة تعيين البيانات التجريبية")
    print("3. إلغاء")
    print()
    
    choice = input("أدخل اختيارك (1-3): ").strip()
    
    if choice == "1":
        print("\n⚠️  تحذير: سيتم تحديث جميع الأسعار الموجودة!")
        confirm = input("هل تريد المتابعة؟ (y/n): ").strip().lower()
        if confirm in ['y', 'yes', 'نعم']:
            update_currency()
        else:
            print("تم إلغاء العملية.")
    
    elif choice == "2":
        print("\n⚠️  تحذير: سيتم حذف جميع البيانات الموجودة!")
        confirm = input("هل تريد المتابعة؟ (y/n): ").strip().lower()
        if confirm in ['y', 'yes', 'نعم']:
            reset_sample_data()
        else:
            print("تم إلغاء العملية.")
    
    elif choice == "3":
        print("تم إلغاء العملية.")
    
    else:
        print("اختيار غير صحيح.")
    
    print("\nاضغط Enter للخروج...")
    input()

if __name__ == "__main__":
    main()

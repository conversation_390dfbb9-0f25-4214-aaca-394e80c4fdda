import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from product_manager import ProductManager
from pos_system import POSSystem
from invoice_manager import InvoiceManager
from reports import ReportsManager

class CashierApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام الكاشير المتقدم")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # تهيئة المكونات
        self.db = DatabaseManager()
        self.product_manager = ProductManager()
        self.pos_system = POSSystem()
        self.invoice_manager = InvoiceManager()
        self.reports_manager = ReportsManager()

        # متغيرات واجهة المستخدم
        self.current_frame = None

        # إنشاء واجهة المستخدم
        self.create_main_interface()

        # تحميل الشاشة الرئيسية
        self.show_pos_screen()

    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # شريط القوائم العلوي
        self.create_menu_bar()

        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # شريط الأدوات
        self.create_toolbar()

        # منطقة المحتوى
        self.content_frame = ttk.Frame(self.main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="نسخة احتياطية", command=self.backup_database)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)

        # قائمة المنتجات
        products_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المنتجات", menu=products_menu)
        products_menu.add_command(label="إدارة المنتجات", command=self.show_products_screen)
        products_menu.add_command(label="إضافة منتج", command=self.show_add_product_dialog)

        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير يومي", command=self.show_daily_report)
        reports_menu.add_command(label="تقرير المخزون", command=self.show_inventory_report)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = ttk.Frame(self.main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # أزرار التنقل
        ttk.Button(toolbar, text="نقطة البيع", command=self.show_pos_screen).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="المنتجات", command=self.show_products_screen).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="التقارير", command=self.show_reports_screen).pack(side=tk.LEFT, padx=(0, 5))

        # معلومات النظام
        info_frame = ttk.Frame(toolbar)
        info_frame.pack(side=tk.RIGHT)

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        ttk.Label(info_frame, text=f"التاريخ: {current_time}").pack(side=tk.RIGHT, padx=(10, 0))

    def clear_content_frame(self):
        """مسح محتوى الإطار الرئيسي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_pos_screen(self):
        """عرض شاشة نقطة البيع"""
        self.clear_content_frame()

        # تقسيم الشاشة
        left_frame = ttk.Frame(self.content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        right_frame = ttk.Frame(self.content_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))

        # منطقة البحث والمنتجات (يسار)
        self.create_product_search_area(left_frame)

        # منطقة السلة والدفع (يمين)
        self.create_cart_area(right_frame)

    def create_product_search_area(self, parent):
        """منطقة البحث عن المنتجات"""
        # عنوان
        ttk.Label(parent, text="البحث عن المنتجات", font=('Arial', 14, 'bold')).pack(pady=(0, 10))

        # منطقة البحث
        search_frame = ttk.Frame(parent)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="البحث:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)
        search_entry.bind('<KeyRelease>', self.on_search_change)

        ttk.Button(search_frame, text="بحث", command=self.search_products).pack(side=tk.LEFT, padx=(5, 0))

        # جدول المنتجات
        self.create_products_table(parent)

    def create_products_table(self, parent):
        """جدول المنتجات"""
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # الجدول
        columns = ('ID', 'الاسم', 'السعر', 'المخزون', 'الفئة')
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعريف الأعمدة
        self.products_tree.heading('ID', text='المعرف')
        self.products_tree.heading('الاسم', text='اسم المنتج')
        self.products_tree.heading('السعر', text='السعر')
        self.products_tree.heading('المخزون', text='المخزون')
        self.products_tree.heading('الفئة', text='الفئة')

        # تحديد عرض الأعمدة
        self.products_tree.column('ID', width=50)
        self.products_tree.column('الاسم', width=200)
        self.products_tree.column('السعر', width=80)
        self.products_tree.column('المخزون', width=80)
        self.products_tree.column('الفئة', width=100)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ربط النقر المزدوج
        self.products_tree.bind('<Double-1>', self.add_product_to_cart)

        # تحميل المنتجات
        self.load_products()

    def create_cart_area(self, parent):
        """منطقة السلة والدفع"""
        parent.configure(width=400)

        # عنوان السلة
        ttk.Label(parent, text="سلة التسوق", font=('Arial', 14, 'bold')).pack(pady=(0, 10))

        # جدول السلة
        cart_frame = ttk.Frame(parent)
        cart_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        columns = ('المنتج', 'الكمية', 'السعر', 'المجموع')
        self.cart_tree = ttk.Treeview(cart_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.cart_tree.heading(col, text=col)
            self.cart_tree.column(col, width=90)

        cart_scrollbar = ttk.Scrollbar(cart_frame, orient=tk.VERTICAL, command=self.cart_tree.yview)
        self.cart_tree.configure(yscrollcommand=cart_scrollbar.set)

        self.cart_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        cart_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار السلة
        cart_buttons_frame = ttk.Frame(parent)
        cart_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(cart_buttons_frame, text="حذف من السلة", command=self.remove_from_cart).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(cart_buttons_frame, text="مسح السلة", command=self.clear_cart).pack(side=tk.LEFT)

        # معلومات المجموع
        total_frame = ttk.LabelFrame(parent, text="المجموع")
        total_frame.pack(fill=tk.X, pady=(0, 10))

        self.total_label = ttk.Label(total_frame, text="0.00 ريال", font=('Arial', 16, 'bold'))
        self.total_label.pack(pady=10)

        # منطقة الدفع
        payment_frame = ttk.LabelFrame(parent, text="الدفع")
        payment_frame.pack(fill=tk.X, pady=(0, 10))

        # طريقة الدفع
        ttk.Label(payment_frame, text="طريقة الدفع:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        self.payment_method = ttk.Combobox(payment_frame, values=["نقدي", "كارت", "محفظة إلكترونية"], state="readonly")
        self.payment_method.pack(fill=tk.X, padx=5, pady=(0, 5))
        self.payment_method.set("نقدي")

        # المبلغ المدفوع (للنقدي)
        ttk.Label(payment_frame, text="المبلغ المدفوع:").pack(anchor=tk.W, padx=5)
        self.amount_paid_var = tk.StringVar()
        self.amount_paid_entry = ttk.Entry(payment_frame, textvariable=self.amount_paid_var)
        self.amount_paid_entry.pack(fill=tk.X, padx=5, pady=(0, 5))

        # زر إتمام البيع
        ttk.Button(payment_frame, text="إتمام البيع", command=self.process_sale,
                  style='Accent.TButton').pack(fill=tk.X, padx=5, pady=5)

    def load_products(self):
        """تحميل المنتجات في الجدول"""
        # مسح الجدول
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        # تحميل المنتجات
        products = self.product_manager.get_all_products()
        for product in products:
            self.products_tree.insert('', 'end', values=product[:6])  # ID, name, barcode, price, category, stock

    def search_products(self):
        """البحث عن المنتجات"""
        search_term = self.search_var.get()

        # مسح الجدول
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        if search_term:
            products = self.product_manager.search_products(search_term)
        else:
            products = self.product_manager.get_all_products()

        for product in products:
            self.products_tree.insert('', 'end', values=product[:6])

    def on_search_change(self, event):
        """البحث التلقائي عند الكتابة"""
        self.search_products()

    def add_product_to_cart(self, event=None):
        """إضافة منتج إلى السلة"""
        selection = self.products_tree.selection()
        if not selection:
            return

        item = self.products_tree.item(selection[0])
        product_id = item['values'][0]

        # طلب الكمية
        quantity = self.ask_quantity()
        if quantity:
            success, message = self.pos_system.add_item_to_cart(product_id=product_id, quantity=quantity)
            if success:
                self.update_cart_display()
            else:
                messagebox.showerror("خطأ", message)

    def ask_quantity(self):
        """طلب الكمية من المستخدم"""
        dialog = tk.Toplevel(self.root)
        dialog.title("الكمية")
        dialog.geometry("300x150")
        dialog.transient(self.root)
        dialog.grab_set()

        # توسيط النافذة
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        ttk.Label(dialog, text="أدخل الكمية:").pack(pady=10)

        quantity_var = tk.StringVar(value="1")
        entry = ttk.Entry(dialog, textvariable=quantity_var, width=20)
        entry.pack(pady=5)
        entry.focus()
        entry.select_range(0, tk.END)

        result = [None]

        def ok_clicked():
            try:
                qty = int(quantity_var.get())
                if qty > 0:
                    result[0] = qty
                    dialog.destroy()
                else:
                    messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح")

        def cancel_clicked():
            dialog.destroy()

        buttons_frame = ttk.Frame(dialog)
        buttons_frame.pack(pady=10)

        ttk.Button(buttons_frame, text="موافق", command=ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=cancel_clicked).pack(side=tk.LEFT, padx=5)

        # ربط Enter بالموافق
        entry.bind('<Return>', lambda e: ok_clicked())

        dialog.wait_window()
        return result[0]

    def update_cart_display(self):
        """تحديث عرض السلة"""
        # مسح السلة
        for item in self.cart_tree.get_children():
            self.cart_tree.delete(item)

        # إضافة العناصر
        cart_summary = self.pos_system.get_cart_summary()
        for item in cart_summary['items']:
            self.cart_tree.insert('', 'end', values=(
                item['name'][:20],  # اختصار الاسم
                item['quantity'],
                f"{item['unit_price']:.2f}",
                f"{item['total_price']:.2f}"
            ))

        # تحديث المجموع
        self.total_label.config(text=f"{cart_summary['total_amount']:.2f} ريال")

    def remove_from_cart(self):
        """حذف منتج من السلة"""
        selection = self.cart_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return

        # الحصول على اسم المنتج
        item = self.cart_tree.item(selection[0])
        product_name = item['values'][0]

        # البحث عن المنتج في السلة
        for cart_item in self.pos_system.current_cart:
            if cart_item['name'].startswith(product_name):
                success, message = self.pos_system.remove_item_from_cart(cart_item['product_id'])
                if success:
                    self.update_cart_display()
                else:
                    messagebox.showerror("خطأ", message)
                break

    def clear_cart(self):
        """مسح السلة"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع العناصر من السلة؟"):
            self.pos_system.clear_cart()
            self.update_cart_display()

    def process_sale(self):
        """معالجة البيع"""
        if not self.pos_system.current_cart:
            messagebox.showwarning("تحذير", "السلة فارغة")
            return

        payment_method = self.payment_method.get()
        amount_paid = None

        if payment_method == "نقدي":
            try:
                amount_paid = float(self.amount_paid_var.get() or 0)
                if amount_paid < self.pos_system.current_total:
                    messagebox.showerror("خطأ", "المبلغ المدفوع أقل من المطلوب")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
                return

        # معالجة البيع
        success, message, invoice_data = self.pos_system.process_payment(
            payment_method, amount_paid, "الكاشير الرئيسي"
        )

        if success:
            # عرض رسالة النجاح
            if payment_method == "نقدي" and invoice_data['change'] > 0:
                messagebox.showinfo("تم البيع",
                    f"تم إتمام البيع بنجاح\nالباقي: {invoice_data['change']:.2f} ريال")
            else:
                messagebox.showinfo("تم البيع", "تم إتمام البيع بنجاح")

            # طباعة الفاتورة
            self.print_invoice_dialog(invoice_data)

            # تحديث العرض
            self.update_cart_display()
            self.load_products()  # تحديث المخزون
            self.amount_paid_var.set("")
        else:
            messagebox.showerror("خطأ", message)

    def print_invoice_dialog(self, invoice_data):
        """حوار طباعة الفاتورة"""
        if messagebox.askyesno("طباعة الفاتورة", "هل تريد طباعة الفاتورة؟"):
            # إنشاء فاتورة PDF
            pdf_path = self.invoice_manager.generate_pdf_invoice(invoice_data)
            if pdf_path:
                messagebox.showinfo("نجح", f"تم حفظ الفاتورة في: {pdf_path}")

                # فتح الفاتورة
                if messagebox.askyesno("فتح الفاتورة", "هل تريد فتح الفاتورة؟"):
                    os.startfile(pdf_path)

    def show_products_screen(self):
        """عرض شاشة إدارة المنتجات"""
        self.clear_content_frame()

        # عنوان
        ttk.Label(self.content_frame, text="إدارة المنتجات",
                 font=('Arial', 16, 'bold')).pack(pady=(0, 20))

        # أزرار الإدارة
        buttons_frame = ttk.Frame(self.content_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(buttons_frame, text="إضافة منتج",
                  command=self.show_add_product_dialog).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="تعديل منتج",
                  command=self.show_edit_product_dialog).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="حذف منتج",
                  command=self.delete_product).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="تحديث",
                  command=self.load_products_management).pack(side=tk.LEFT, padx=(0, 5))

        # جدول المنتجات للإدارة
        self.create_products_management_table()

    def create_products_management_table(self):
        """جدول إدارة المنتجات"""
        table_frame = ttk.Frame(self.content_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)

        columns = ('ID', 'الاسم', 'الباركود', 'السعر', 'المخزون', 'الفئة', 'تاريخ الإضافة')
        self.products_mgmt_tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        # تعريف الأعمدة
        for col in columns:
            self.products_mgmt_tree.heading(col, text=col)
            self.products_mgmt_tree.column(col, width=120)

        # شريط التمرير
        mgmt_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL,
                                      command=self.products_mgmt_tree.yview)
        self.products_mgmt_tree.configure(yscrollcommand=mgmt_scrollbar.set)

        self.products_mgmt_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        mgmt_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # تحميل البيانات
        self.load_products_management()

    def load_products_management(self):
        """تحميل المنتجات في جدول الإدارة"""
        for item in self.products_mgmt_tree.get_children():
            self.products_mgmt_tree.delete(item)

        products = self.product_manager.get_all_products()
        for product in products:
            self.products_mgmt_tree.insert('', 'end', values=product)

    def show_add_product_dialog(self):
        """حوار إضافة منتج"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة منتج جديد")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # توسيط النافذة
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # الحقول
        ttk.Label(dialog, text="اسم المنتج:").pack(anchor=tk.W, padx=10, pady=(10, 0))
        name_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=name_var, width=40).pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Label(dialog, text="السعر:").pack(anchor=tk.W, padx=10)
        price_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=price_var, width=40).pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Label(dialog, text="الفئة:").pack(anchor=tk.W, padx=10)
        category_var = tk.StringVar()
        category_combo = ttk.Combobox(dialog, textvariable=category_var, width=37)
        category_combo.pack(fill=tk.X, padx=10, pady=(0, 10))

        # تحميل الفئات
        categories = self.product_manager.get_all_categories()
        category_values = [cat[1] for cat in categories] + ["عام", "طعام", "مشروبات", "أدوات"]
        category_combo['values'] = category_values
        category_combo.set("عام")

        ttk.Label(dialog, text="المخزون:").pack(anchor=tk.W, padx=10)
        stock_var = tk.StringVar(value="0")
        ttk.Entry(dialog, textvariable=stock_var, width=40).pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Label(dialog, text="الباركود (اختياري):").pack(anchor=tk.W, padx=10)
        barcode_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=barcode_var, width=40).pack(fill=tk.X, padx=10, pady=(0, 20))

        def save_product():
            try:
                name = name_var.get().strip()
                price = float(price_var.get())
                category = category_var.get().strip() or "عام"
                stock = int(stock_var.get() or 0)
                barcode = barcode_var.get().strip() or None

                if not name:
                    messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج")
                    return

                product_id = self.product_manager.add_product(name, price, category, barcode, stock)
                if product_id:
                    messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")
                    dialog.destroy()
                    if hasattr(self, 'products_mgmt_tree'):
                        self.load_products_management()
                    self.load_products()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة المنتج")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى التأكد من صحة البيانات المدخلة")

        # الأزرار
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(buttons_frame, text="حفظ", command=save_product).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side=tk.LEFT)

    def show_edit_product_dialog(self):
        """حوار تعديل منتج"""
        selection = self.products_mgmt_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
            return

        item = self.products_mgmt_tree.item(selection[0])
        product_data = item['values']
        product_id = product_data[0]

        # نفس حوار الإضافة مع تعبئة البيانات
        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل منتج")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # الحقول مع البيانات الحالية
        ttk.Label(dialog, text="اسم المنتج:").pack(anchor=tk.W, padx=10, pady=(10, 0))
        name_var = tk.StringVar(value=product_data[1])
        ttk.Entry(dialog, textvariable=name_var, width=40).pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Label(dialog, text="السعر:").pack(anchor=tk.W, padx=10)
        price_var = tk.StringVar(value=str(product_data[3]))
        ttk.Entry(dialog, textvariable=price_var, width=40).pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Label(dialog, text="الفئة:").pack(anchor=tk.W, padx=10)
        category_var = tk.StringVar(value=product_data[5])
        category_combo = ttk.Combobox(dialog, textvariable=category_var, width=37)
        category_combo.pack(fill=tk.X, padx=10, pady=(0, 10))

        categories = self.product_manager.get_all_categories()
        category_values = [cat[1] for cat in categories] + ["عام", "طعام", "مشروبات", "أدوات"]
        category_combo['values'] = category_values

        ttk.Label(dialog, text="المخزون:").pack(anchor=tk.W, padx=10)
        stock_var = tk.StringVar(value=str(product_data[4]))
        ttk.Entry(dialog, textvariable=stock_var, width=40).pack(fill=tk.X, padx=10, pady=(0, 20))

        def update_product():
            try:
                name = name_var.get().strip()
                price = float(price_var.get())
                category = category_var.get().strip()
                stock = int(stock_var.get())

                if not name:
                    messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج")
                    return

                success = self.product_manager.update_product(
                    product_id, name=name, price=price, category=category, stock_quantity=stock
                )

                if success:
                    messagebox.showinfo("نجح", "تم تحديث المنتج بنجاح")
                    dialog.destroy()
                    self.load_products_management()
                    self.load_products()
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث المنتج")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى التأكد من صحة البيانات المدخلة")

        buttons_frame = ttk.Frame(dialog)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(buttons_frame, text="تحديث", command=update_product).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side=tk.LEFT)

    def delete_product(self):
        """حذف منتج"""
        selection = self.products_mgmt_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return

        item = self.products_mgmt_tree.item(selection[0])
        product_name = item['values'][1]
        product_id = item['values'][0]

        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المنتج '{product_name}'؟"):
            success = self.product_manager.delete_product(product_id)
            if success:
                messagebox.showinfo("نجح", "تم حذف المنتج بنجاح")
                self.load_products_management()
                self.load_products()
            else:
                messagebox.showerror("خطأ", "فشل في حذف المنتج")

    def show_reports_screen(self):
        """عرض شاشة التقارير"""
        self.clear_content_frame()

        ttk.Label(self.content_frame, text="التقارير والإحصائيات",
                 font=('Arial', 16, 'bold')).pack(pady=(0, 20))

        # أزرار التقارير
        reports_frame = ttk.Frame(self.content_frame)
        reports_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Button(reports_frame, text="تقرير يومي",
                  command=self.show_daily_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(reports_frame, text="تقرير أسبوعي",
                  command=self.show_weekly_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(reports_frame, text="تقرير المخزون",
                  command=self.show_inventory_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(reports_frame, text="أداء المنتجات",
                  command=self.show_product_performance).pack(side=tk.LEFT, padx=(0, 10))

        # منطقة عرض التقارير
        self.reports_display_frame = ttk.Frame(self.content_frame)
        self.reports_display_frame.pack(fill=tk.BOTH, expand=True)

    def show_daily_report(self):
        """عرض التقرير اليومي"""
        # مسح المنطقة
        for widget in self.reports_display_frame.winfo_children():
            widget.destroy()

        report = self.reports_manager.get_daily_sales_report()
        if not report:
            ttk.Label(self.reports_display_frame, text="لا توجد بيانات للعرض").pack(pady=20)
            return

        # عرض الملخص
        summary_frame = ttk.LabelFrame(self.reports_display_frame, text="ملخص اليوم")
        summary_frame.pack(fill=tk.X, pady=(0, 10))

        summary = report['summary']
        ttk.Label(summary_frame, text=f"التاريخ: {report['date']}").pack(anchor=tk.W, padx=10, pady=2)
        ttk.Label(summary_frame, text=f"عدد المعاملات: {summary[0]}").pack(anchor=tk.W, padx=10, pady=2)
        ttk.Label(summary_frame, text=f"إجمالي المبيعات: {summary[1]:.2f} ريال").pack(anchor=tk.W, padx=10, pady=2)
        ttk.Label(summary_frame, text=f"متوسط البيع: {summary[2]:.2f} ريال").pack(anchor=tk.W, padx=10, pady=2)

        # طرق الدفع
        if report['payment_methods']:
            payment_frame = ttk.LabelFrame(self.reports_display_frame, text="طرق الدفع")
            payment_frame.pack(fill=tk.X, pady=(0, 10))

            for method in report['payment_methods']:
                ttk.Label(payment_frame,
                         text=f"{method[0]}: {method[1]} معاملة - {method[2]:.2f} ريال").pack(
                    anchor=tk.W, padx=10, pady=2)

        # أزرار التصدير
        export_frame = ttk.Frame(self.reports_display_frame)
        export_frame.pack(fill=tk.X, pady=10)

        ttk.Button(export_frame, text="تصدير إلى Excel",
                  command=lambda: self.export_report(report, 'daily')).pack(side=tk.LEFT, padx=(0, 5))

    def show_weekly_report(self):
        """عرض التقرير الأسبوعي"""
        for widget in self.reports_display_frame.winfo_children():
            widget.destroy()

        report = self.reports_manager.get_weekly_sales_report()
        if not report:
            ttk.Label(self.reports_display_frame, text="لا توجد بيانات للعرض").pack(pady=20)
            return

        summary_frame = ttk.LabelFrame(self.reports_display_frame, text="ملخص الأسبوع")
        summary_frame.pack(fill=tk.X, pady=(0, 10))

        summary = report['summary']
        ttk.Label(summary_frame, text=f"من: {report['start_date']} إلى: {report['end_date']}").pack(anchor=tk.W, padx=10, pady=2)
        ttk.Label(summary_frame, text=f"عدد المعاملات: {summary[0]}").pack(anchor=tk.W, padx=10, pady=2)
        ttk.Label(summary_frame, text=f"إجمالي المبيعات: {summary[1]:.2f} ريال").pack(anchor=tk.W, padx=10, pady=2)

    def show_inventory_report(self):
        """عرض تقرير المخزون"""
        for widget in self.reports_display_frame.winfo_children():
            widget.destroy()

        report = self.reports_manager.get_inventory_report()
        if not report:
            ttk.Label(self.reports_display_frame, text="لا توجد بيانات للعرض").pack(pady=20)
            return

        # إحصائيات عامة
        stats_frame = ttk.LabelFrame(self.reports_display_frame, text="إحصائيات المخزون")
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        stats = report['statistics']
        ttk.Label(stats_frame, text=f"إجمالي المنتجات: {stats[0]}").pack(anchor=tk.W, padx=10, pady=2)
        ttk.Label(stats_frame, text=f"إجمالي القطع: {stats[1]}").pack(anchor=tk.W, padx=10, pady=2)
        ttk.Label(stats_frame, text=f"قيمة المخزون: {stats[2]:.2f} ريال").pack(anchor=tk.W, padx=10, pady=2)

        # المنتجات منخفضة المخزون
        if report['low_stock_products']:
            low_stock_frame = ttk.LabelFrame(self.reports_display_frame, text="منتجات منخفضة المخزون")
            low_stock_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            columns = ('المنتج', 'المخزون', 'السعر', 'الفئة')
            low_stock_tree = ttk.Treeview(low_stock_frame, columns=columns, show='headings', height=8)

            for col in columns:
                low_stock_tree.heading(col, text=col)
                low_stock_tree.column(col, width=120)

            for product in report['low_stock_products']:
                low_stock_tree.insert('', 'end', values=product)

            low_stock_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # تصدير
        ttk.Button(self.reports_display_frame, text="تصدير إلى Excel",
                  command=lambda: self.export_report(report, 'inventory')).pack(pady=10)

    def show_product_performance(self):
        """عرض تقرير أداء المنتجات"""
        for widget in self.reports_display_frame.winfo_children():
            widget.destroy()

        report = self.reports_manager.get_product_performance_report()
        if not report:
            ttk.Label(self.reports_display_frame, text="لا توجد بيانات للعرض").pack(pady=20)
            return

        ttk.Label(self.reports_display_frame,
                 text=f"أداء المنتجات - آخر {report['period_days']} يوم",
                 font=('Arial', 12, 'bold')).pack(pady=(0, 10))

        # جدول الأداء
        columns = ('المنتج', 'الفئة', 'المباع', 'الإيرادات', 'المخزون الحالي')
        performance_tree = ttk.Treeview(self.reports_display_frame, columns=columns, show='headings')

        for col in columns:
            performance_tree.heading(col, text=col)
            performance_tree.column(col, width=120)

        for product in report['products'][:20]:  # أفضل 20 منتج
            performance_tree.insert('', 'end', values=(
                product[0][:30],  # اسم المنتج
                product[1],       # الفئة
                product[2] or 0,  # المباع
                f"{product[3] or 0:.2f}",  # الإيرادات
                product[6]        # المخزون الحالي
            ))

        performance_tree.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    def export_report(self, report_data, report_type):
        """تصدير التقرير"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
            title="حفظ التقرير"
        )

        if filename:
            exported_file = self.reports_manager.export_report_to_excel(
                report_data, report_type, filename
            )
            if exported_file:
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى: {exported_file}")
            else:
                messagebox.showerror("خطأ", "فشل في تصدير التقرير")

    def backup_database(self):
        """إنشاء نسخة احتياطية"""
        try:
            backup_path = self.db.backup_to_excel()
            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية في: {backup_path}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {e}")

    def show_about(self):
        """حول البرنامج"""
        about_text = """
نظام الكاشير المتقدم
الإصدار 1.0

برنامج شامل لإدارة نقطة البيع يتضمن:
• إدارة المنتجات والمخزون
• نظام نقطة بيع متقدم
• طباعة الفواتير
• التقارير والإحصائيات
• النسخ الاحتياطية

تم تطويره باستخدام Python و Tkinter
        """
        messagebox.showinfo("حول البرنامج", about_text)


def main():
    """تشغيل البرنامج"""
    root = tk.Tk()
    app = CashierApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()

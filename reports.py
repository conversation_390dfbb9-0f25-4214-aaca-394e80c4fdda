from database import DatabaseManager
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import os

class ReportsManager:
    def __init__(self):
        self.db = DatabaseManager()
        self.ensure_reports_directory()

    def ensure_reports_directory(self):
        """إنشاء مجلد التقارير"""
        os.makedirs("data/reports", exist_ok=True)

    def get_daily_sales_report(self, date=None):
        """تقرير المبيعات اليومية"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')

        try:
            # إجمالي المبيعات
            total_query = '''
                SELECT
                    COUNT(*) as total_transactions,
                    SUM(total_amount) as total_sales,
                    AVG(total_amount) as average_sale
                FROM sales
                WHERE DATE(sale_date) = ?
            '''
            total_data = self.db.execute_query(total_query, (date,))

            # المبيعات حسب طريقة الدفع
            payment_query = '''
                SELECT
                    payment_method,
                    COUNT(*) as transaction_count,
                    SUM(total_amount) as total_amount
                FROM sales
                WHERE DATE(sale_date) = ?
                GROUP BY payment_method
            '''
            payment_data = self.db.execute_query(payment_query, (date,))

            # أكثر المنتجات مبيعاً
            top_products_query = '''
                SELECT
                    p.name,
                    SUM(si.quantity) as total_quantity,
                    SUM(si.total_price) as total_revenue
                FROM sale_items si
                JOIN products p ON si.product_id = p.id
                JOIN sales s ON si.sale_id = s.id
                WHERE DATE(s.sale_date) = ?
                GROUP BY p.id, p.name
                ORDER BY total_quantity DESC
                LIMIT 10
            '''
            top_products = self.db.execute_query(top_products_query, (date,))

            # المبيعات بالساعة
            hourly_query = '''
                SELECT
                    strftime('%H', sale_date) as hour,
                    COUNT(*) as transaction_count,
                    SUM(total_amount) as total_sales
                FROM sales
                WHERE DATE(sale_date) = ?
                GROUP BY strftime('%H', sale_date)
                ORDER BY hour
            '''
            hourly_data = self.db.execute_query(hourly_query, (date,))

            report = {
                'date': date,
                'summary': total_data[0] if total_data else (0, 0, 0),
                'payment_methods': payment_data,
                'top_products': top_products,
                'hourly_sales': hourly_data
            }

            return report

        except Exception as e:
            print(f"خطأ في إنشاء التقرير اليومي: {e}")
            return None

    def get_weekly_sales_report(self, start_date=None):
        """تقرير المبيعات الأسبوعية"""
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

        end_date = (datetime.strptime(start_date, '%Y-%m-%d') + timedelta(days=7)).strftime('%Y-%m-%d')

        try:
            # المبيعات اليومية خلال الأسبوع
            daily_query = '''
                SELECT
                    DATE(sale_date) as sale_date,
                    COUNT(*) as transaction_count,
                    SUM(total_amount) as total_sales
                FROM sales
                WHERE DATE(sale_date) BETWEEN ? AND ?
                GROUP BY DATE(sale_date)
                ORDER BY sale_date
            '''
            daily_data = self.db.execute_query(daily_query, (start_date, end_date))

            # إجمالي الأسبوع
            total_query = '''
                SELECT
                    COUNT(*) as total_transactions,
                    SUM(total_amount) as total_sales,
                    AVG(total_amount) as average_sale
                FROM sales
                WHERE DATE(sale_date) BETWEEN ? AND ?
            '''
            total_data = self.db.execute_query(total_query, (start_date, end_date))

            report = {
                'start_date': start_date,
                'end_date': end_date,
                'daily_sales': daily_data,
                'summary': total_data[0] if total_data else (0, 0, 0)
            }

            return report

        except Exception as e:
            print(f"خطأ في إنشاء التقرير الأسبوعي: {e}")
            return None

    def get_monthly_sales_report(self, year=None, month=None):
        """تقرير المبيعات الشهرية"""
        if year is None:
            year = datetime.now().year
        if month is None:
            month = datetime.now().month

        try:
            # المبيعات اليومية خلال الشهر
            daily_query = '''
                SELECT
                    strftime('%d', sale_date) as day,
                    COUNT(*) as transaction_count,
                    SUM(total_amount) as total_sales
                FROM sales
                WHERE strftime('%Y', sale_date) = ? AND strftime('%m', sale_date) = ?
                GROUP BY strftime('%d', sale_date)
                ORDER BY day
            '''
            daily_data = self.db.execute_query(daily_query, (str(year), f"{month:02d}"))

            # إجمالي الشهر
            total_query = '''
                SELECT
                    COUNT(*) as total_transactions,
                    SUM(total_amount) as total_sales,
                    AVG(total_amount) as average_sale
                FROM sales
                WHERE strftime('%Y', sale_date) = ? AND strftime('%m', sale_date) = ?
            '''
            total_data = self.db.execute_query(total_query, (str(year), f"{month:02d}"))

            report = {
                'year': year,
                'month': month,
                'daily_sales': daily_data,
                'summary': total_data[0] if total_data else (0, 0, 0)
            }

            return report

        except Exception as e:
            print(f"خطأ في إنشاء التقرير الشهري: {e}")
            return None

    def get_product_performance_report(self, days=30):
        """تقرير أداء المنتجات"""
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

        try:
            query = '''
                SELECT
                    p.name,
                    p.category,
                    SUM(si.quantity) as total_sold,
                    SUM(si.total_price) as total_revenue,
                    AVG(si.unit_price) as avg_price,
                    COUNT(DISTINCT s.id) as transaction_count,
                    p.stock_quantity as current_stock
                FROM products p
                LEFT JOIN sale_items si ON p.id = si.product_id
                LEFT JOIN sales s ON si.sale_id = s.id AND DATE(s.sale_date) >= ?
                GROUP BY p.id, p.name, p.category, p.stock_quantity
                ORDER BY total_sold DESC NULLS LAST
            '''

            data = self.db.execute_query(query, (start_date,))

            report = {
                'period_days': days,
                'start_date': start_date,
                'products': data
            }

            return report

        except Exception as e:
            print(f"خطأ في إنشاء تقرير أداء المنتجات: {e}")
            return None

    def get_inventory_report(self):
        """تقرير المخزون"""
        try:
            # المنتجات منخفضة المخزون
            low_stock_query = '''
                SELECT name, stock_quantity, price, category
                FROM products
                WHERE stock_quantity <= 5
                ORDER BY stock_quantity ASC
            '''
            low_stock = self.db.execute_query(low_stock_query)

            # إحصائيات المخزون
            stats_query = '''
                SELECT
                    COUNT(*) as total_products,
                    SUM(stock_quantity) as total_items,
                    SUM(stock_quantity * price) as total_value,
                    AVG(stock_quantity) as avg_stock
                FROM products
            '''
            stats = self.db.execute_query(stats_query)

            # المخزون حسب الفئة
            category_query = '''
                SELECT
                    category,
                    COUNT(*) as product_count,
                    SUM(stock_quantity) as total_items,
                    SUM(stock_quantity * price) as total_value
                FROM products
                GROUP BY category
                ORDER BY total_value DESC
            '''
            by_category = self.db.execute_query(category_query)

            report = {
                'low_stock_products': low_stock,
                'statistics': stats[0] if stats else (0, 0, 0, 0),
                'by_category': by_category
            }

            return report

        except Exception as e:
            print(f"خطأ في إنشاء تقرير المخزون: {e}")
            return None

    def export_report_to_excel(self, report_data, report_type, filename=None):
        """تصدير التقرير إلى Excel"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"data/reports/{report_type}_{timestamp}.xlsx"

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                if report_type == 'daily':
                    # ملخص اليوم
                    summary_df = pd.DataFrame([{
                        'التاريخ': report_data['date'],
                        'عدد المعاملات': report_data['summary'][0],
                        'إجمالي المبيعات': report_data['summary'][1],
                        'متوسط البيع': report_data['summary'][2]
                    }])
                    summary_df.to_excel(writer, sheet_name='الملخص', index=False)

                    # طرق الدفع
                    if report_data['payment_methods']:
                        payment_df = pd.DataFrame(report_data['payment_methods'],
                                                columns=['طريقة الدفع', 'عدد المعاملات', 'المبلغ'])
                        payment_df.to_excel(writer, sheet_name='طرق_الدفع', index=False)

                    # أكثر المنتجات مبيعاً
                    if report_data['top_products']:
                        products_df = pd.DataFrame(report_data['top_products'],
                                                 columns=['المنتج', 'الكمية المباعة', 'الإيرادات'])
                        products_df.to_excel(writer, sheet_name='أكثر_المنتجات_مبيعا', index=False)

                elif report_type == 'inventory':
                    # المنتجات منخفضة المخزون
                    if report_data['low_stock_products']:
                        low_stock_df = pd.DataFrame(report_data['low_stock_products'],
                                                  columns=['المنتج', 'المخزون', 'السعر', 'الفئة'])
                        low_stock_df.to_excel(writer, sheet_name='مخزون_منخفض', index=False)

                    # المخزون حسب الفئة
                    if report_data['by_category']:
                        category_df = pd.DataFrame(report_data['by_category'],
                                                 columns=['الفئة', 'عدد المنتجات', 'إجمالي القطع', 'إجمالي القيمة'])
                        category_df.to_excel(writer, sheet_name='المخزون_حسب_الفئة', index=False)

            return filename

        except Exception as e:
            print(f"خطأ في تصدير التقرير: {e}")
            return None

    def create_sales_chart(self, report_data, chart_type='daily'):
        """إنشاء رسم بياني للمبيعات"""
        try:
            plt.figure(figsize=(12, 6))
            plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']

            if chart_type == 'daily' and report_data.get('hourly_sales'):
                hours = [int(row[0]) for row in report_data['hourly_sales']]
                sales = [row[2] for row in report_data['hourly_sales']]

                plt.bar(hours, sales)
                plt.title(f"المبيعات بالساعة - {report_data['date']}")
                plt.xlabel('الساعة')
                plt.ylabel('المبيعات (دينار عراقي)')
                plt.xticks(range(0, 24))
                plt.grid(True, alpha=0.3)

            elif chart_type == 'weekly' and report_data.get('daily_sales'):
                dates = [row[0] for row in report_data['daily_sales']]
                sales = [row[2] for row in report_data['daily_sales']]

                plt.plot(dates, sales, marker='o')
                plt.title(f"المبيعات الأسبوعية - {report_data['start_date']} إلى {report_data['end_date']}")
                plt.xlabel('التاريخ')
                plt.ylabel('المبيعات (دينار عراقي)')
                plt.xticks(rotation=45)
                plt.grid(True, alpha=0.3)

            plt.tight_layout()

            # حفظ الرسم البياني
            chart_filename = f"data/reports/chart_{chart_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            plt.close()

            return chart_filename

        except Exception as e:
            print(f"خطأ في إنشاء الرسم البياني: {e}")
            return None

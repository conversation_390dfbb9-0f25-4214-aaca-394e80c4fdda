# نظام الكاشير المتقدم

برنامج شامل لإدارة نقطة البيع مطور بلغة Python مع واجهة مستخدم حديثة.

## المميزات الرئيسية

### 🛍️ إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- إدارة المخزون والكميات
- تصنيف المنتجات حسب الفئات
- إنشاء باركود تلقائي لكل منتج
- البحث السريع في المنتجات

### 💰 نظام نقطة البيع (POS)
- واجهة بيع سهلة وسريعة
- إضافة المنتجات عن طريق الباركود أو البحث
- حساب المجموع الكلي تلقائياً
- دعم طرق دفع متعددة (نقدي، كارت، محفظة إلكترونية)
- حساب الباقي للدفع النقدي

### 🧾 إدارة الفواتير
- طباعة الفواتير بصيغة PDF
- حفظ سجل مفصل لجميع المبيعات
- إمكانية إعادة طباعة الفواتير
- تصدير الفواتير بصيغة نصية

### 📊 التقارير والإحصائيات
- تقرير المبيعات اليومية
- تقرير المبيعات الأسبوعية والشهرية
- تقرير المخزون والمنتجات منخفضة المخزون
- تقرير أداء المنتجات
- تصدير التقارير إلى Excel

### 💾 النسخ الاحتياطية
- إنشاء نسخ احتياطية تلقائية
- تصدير البيانات إلى Excel
- حفظ البيانات محلياً في قاعدة بيانات SQLite

## متطلبات النظام

- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux
- ذاكرة: 512 MB RAM كحد أدنى
- مساحة القرص: 100 MB

## التثبيت والإعداد

### 1. تحميل البرنامج
```bash
git clone [repository-url]
cd كاشير
```

### 2. تشغيل الإعداد التلقائي
```bash
python setup.py
```

سيقوم الإعداد بـ:
- تثبيت جميع المكتبات المطلوبة
- إنشاء المجلدات اللازمة
- إضافة بيانات تجريبية (اختياري)

### 3. تشغيل البرنامج
```bash
python main.py
```

## كيفية الاستخدام

### البدء السريع

1. **تشغيل البرنامج**: قم بتشغيل `python main.py`
2. **إضافة منتجات**: اذهب إلى قائمة "المنتجات" > "إضافة منتج"
3. **بدء البيع**: في الشاشة الرئيسية، ابحث عن المنتج وانقر عليه مرتين لإضافته للسلة
4. **إتمام البيع**: اختر طريقة الدفع واضغط "إتمام البيع"

### الشاشات الرئيسية

#### 🏪 شاشة نقطة البيع
- **البحث**: ابحث عن المنتجات بالاسم أو الباركود
- **السلة**: عرض المنتجات المضافة مع الكميات والأسعار
- **الدفع**: اختيار طريقة الدفع وإدخال المبلغ المدفوع

#### 📦 إدارة المنتجات
- **إضافة منتج جديد**: اسم، سعر، فئة، مخزون، باركود
- **تعديل المنتجات**: تحديث بيانات المنتجات الموجودة
- **حذف المنتجات**: إزالة المنتجات غير المرغوبة

#### 📈 التقارير
- **التقرير اليومي**: ملخص مبيعات اليوم مع تفاصيل طرق الدفع
- **تقرير المخزون**: حالة المخزون والمنتجات منخفضة المخزون
- **أداء المنتجات**: أكثر المنتجات مبيعاً وأرباحاً

## هيكل الملفات

```
كاشير/
├── main.py                 # الملف الرئيسي للبرنامج
├── database.py             # إدارة قاعدة البيانات
├── product_manager.py      # إدارة المنتجات
├── pos_system.py          # نظام نقطة البيع
├── invoice_manager.py     # إدارة الفواتير
├── reports.py             # التقارير والإحصائيات
├── setup.py               # إعداد البرنامج
├── requirements.txt       # المكتبات المطلوبة
├── README.md              # دليل الاستخدام
└── data/                  # مجلد البيانات
    ├── cashier.db         # قاعدة البيانات
    ├── invoices/          # الفواتير المحفوظة
    ├── reports/           # التقارير المصدرة
    └── barcodes/          # صور الباركود
```

## المكتبات المستخدمة

- **tkinter**: واجهة المستخدم الرسومية
- **sqlite3**: قاعدة البيانات المحلية
- **pandas**: معالجة البيانات
- **openpyxl**: تصدير Excel
- **reportlab**: إنشاء ملفات PDF
- **python-barcode**: إنشاء الباركود
- **matplotlib**: الرسوم البيانية

## الميزات المتقدمة

### 🔍 البحث الذكي
- البحث بالاسم، الباركود، أو الفئة
- البحث التلقائي أثناء الكتابة
- عرض النتائج فورياً

### 📱 واجهة سهلة الاستخدام
- تصميم حديث ومتجاوب
- دعم اللغة العربية
- اختصارات لوحة المفاتيح

### 🔒 الأمان والموثوقية
- حفظ تلقائي للبيانات
- نسخ احتياطية منتظمة
- التحقق من صحة البيانات

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ في تثبيت المكتبات:**
```bash
pip install --upgrade pip
python setup.py
```

**خطأ في قاعدة البيانات:**
- تأكد من وجود مجلد `data`
- احذف ملف `data/cashier.db` وأعد تشغيل البرنامج

**مشاكل في الطباعة:**
- تأكد من تثبيت طابعة افتراضية
- تحقق من إعدادات الطابعة في النظام

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع هذا الدليل أولاً
- تحقق من رسائل الخطأ في وحدة التحكم
- تأكد من تثبيت جميع المكتبات المطلوبة

## التطوير المستقبلي

### ميزات مخططة:
- [ ] دعم ماسح الباركود
- [ ] تطبيق ويب
- [ ] تزامن البيانات السحابية
- [ ] تقارير متقدمة
- [ ] دعم عدة مستخدمين

## الترخيص

هذا البرنامج مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**تم تطوير هذا البرنامج باستخدام Python و Tkinter**

للمزيد من المعلومات أو الدعم الفني، يرجى مراجعة الوثائق أو التواصل مع فريق التطوير.

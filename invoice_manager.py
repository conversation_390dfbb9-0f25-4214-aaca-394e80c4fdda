from reportlab.lib.pagesizes import A4, letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
from datetime import datetime

class InvoiceManager:
    def __init__(self):
        self.setup_fonts()
        self.ensure_invoices_directory()

    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # يمكن إضافة خط عربي هنا إذا كان متوفراً
            pass
        except:
            pass

    def ensure_invoices_directory(self):
        """إنشاء مجلد الفواتير"""
        os.makedirs("data/invoices", exist_ok=True)

    def generate_pdf_invoice(self, invoice_data, save_path=None):
        """إنشاء فاتورة PDF"""
        try:
            if not save_path:
                save_path = f"data/invoices/invoice_{invoice_data['invoice_number']}.pdf"

            doc = SimpleDocTemplate(save_path, pagesize=A4)
            story = []
            styles = getSampleStyleSheet()

            # عنوان الفاتورة
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # وسط
            )

            story.append(Paragraph("فاتورة مبيعات", title_style))
            story.append(Spacer(1, 12))

            # معلومات الفاتورة
            info_data = [
                ['رقم الفاتورة:', invoice_data['invoice_number']],
                ['التاريخ:', invoice_data['date']],
                ['الكاشير:', invoice_data.get('cashier', 'غير محدد')],
                ['طريقة الدفع:', invoice_data['payment_method']]
            ]

            info_table = Table(info_data, colWidths=[2*inch, 3*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('BACKGROUND', (1, 0), (1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(info_table)
            story.append(Spacer(1, 20))

            # جدول المنتجات
            items_data = [['المنتج', 'الكمية', 'السعر', 'المجموع']]

            for item in invoice_data['items']:
                items_data.append([
                    item['name'],
                    str(item['quantity']),
                    f"{item['unit_price']:.0f}",
                    f"{item['total_price']:.0f}"
                ])

            items_table = Table(items_data, colWidths=[3*inch, 1*inch, 1.5*inch, 1.5*inch])
            items_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(items_table)
            story.append(Spacer(1, 20))

            # المجموع النهائي
            total_data = [
                ['المجموع الكلي:', f"{invoice_data['total']:.0f} دينار عراقي"]
            ]

            if invoice_data.get('amount_paid'):
                total_data.append(['المبلغ المدفوع:', f"{invoice_data['amount_paid']:.0f} دينار عراقي"])
                total_data.append(['الباقي:', f"{invoice_data.get('change', 0):.0f} دينار عراقي"])

            total_table = Table(total_data, colWidths=[3*inch, 2*inch])
            total_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('BACKGROUND', (1, 0), (1, -1), colors.yellow),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(total_table)
            story.append(Spacer(1, 30))

            # رسالة شكر
            thank_you = Paragraph("شكراً لتسوقكم معنا", styles['Normal'])
            story.append(thank_you)

            # بناء PDF
            doc.build(story)
            return save_path

        except Exception as e:
            print(f"خطأ في إنشاء فاتورة PDF: {e}")
            return None

    def generate_text_invoice(self, invoice_data):
        """إنشاء فاتورة نصية للطباعة السريعة"""
        try:
            invoice_text = f"""
{'='*50}
                فاتورة مبيعات
{'='*50}

رقم الفاتورة: {invoice_data['invoice_number']}
التاريخ: {invoice_data['date']}
الكاشير: {invoice_data.get('cashier', 'غير محدد')}
طريقة الدفع: {invoice_data['payment_method']}

{'-'*50}
المنتجات:
{'-'*50}
"""

            for item in invoice_data['items']:
                invoice_text += f"{item['name']:<20} {item['quantity']:>5} x {item['unit_price']:>8.0f} = {item['total_price']:>10.0f}\n"

            invoice_text += f"""
{'-'*50}
المجموع الكلي: {invoice_data['total']:>10.0f} دينار عراقي
"""

            if invoice_data.get('amount_paid'):
                invoice_text += f"المبلغ المدفوع: {invoice_data['amount_paid']:>10.0f} دينار عراقي\n"
                invoice_text += f"الباقي: {invoice_data.get('change', 0):>10.0f} دينار عراقي\n"

            invoice_text += f"""
{'='*50}
        شكراً لتسوقكم معنا
{'='*50}
"""

            return invoice_text

        except Exception as e:
            print(f"خطأ في إنشاء الفاتورة النصية: {e}")
            return None

    def save_text_invoice(self, invoice_data, save_path=None):
        """حفظ الفاتورة النصية في ملف"""
        try:
            if not save_path:
                save_path = f"data/invoices/invoice_{invoice_data['invoice_number']}.txt"

            invoice_text = self.generate_text_invoice(invoice_data)

            if invoice_text:
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(invoice_text)
                return save_path

            return None

        except Exception as e:
            print(f"خطأ في حفظ الفاتورة النصية: {e}")
            return None

    def print_invoice(self, invoice_data, printer_name=None):
        """طباعة الفاتورة (يتطلب إعداد الطابعة)"""
        try:
            # إنشاء الفاتورة النصية
            invoice_text = self.generate_text_invoice(invoice_data)

            if not invoice_text:
                return False

            # حفظ مؤقت للطباعة
            temp_file = f"data/invoices/temp_print_{invoice_data['invoice_number']}.txt"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(invoice_text)

            # طباعة الملف (يمكن تخصيص هذا حسب نوع الطابعة)
            import subprocess
            if os.name == 'nt':  # Windows
                subprocess.run(['notepad', '/p', temp_file], check=True)
            else:  # Linux/Mac
                subprocess.run(['lp', temp_file], check=True)

            # حذف الملف المؤقت
            os.remove(temp_file)
            return True

        except Exception as e:
            print(f"خطأ في طباعة الفاتورة: {e}")
            return False

    def get_invoice_template_data(self, sale_id, db_manager):
        """الحصول على بيانات الفاتورة من قاعدة البيانات"""
        try:
            # بيانات البيع
            sale_query = "SELECT * FROM sales WHERE id = ?"
            sale_data = db_manager.execute_query(sale_query, (sale_id,))

            if not sale_data:
                return None

            sale = sale_data[0]

            # تفاصيل المنتجات
            items_query = '''
                SELECT si.*, p.name, p.barcode
                FROM sale_items si
                JOIN products p ON si.product_id = p.id
                WHERE si.sale_id = ?
            '''
            items_data = db_manager.execute_query(items_query, (sale_id,))

            # تنسيق البيانات
            items = []
            for item in items_data:
                items.append({
                    'product_id': item[2],
                    'name': item[6],
                    'barcode': item[7],
                    'quantity': item[3],
                    'unit_price': item[4],
                    'total_price': item[5]
                })

            invoice_data = {
                'sale_id': sale[0],
                'invoice_number': sale[1],
                'total': sale[2],
                'payment_method': sale[3],
                'date': sale[4],
                'cashier': sale[5] if sale[5] else 'غير محدد',
                'items': items
            }

            return invoice_data

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الفاتورة: {e}")
            return None

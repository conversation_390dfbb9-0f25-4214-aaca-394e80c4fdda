from database import DatabaseManager
import barcode
from barcode.writer import ImageWriter
import os

class ProductManager:
    def __init__(self):
        self.db = DatabaseManager()
    
    def add_product(self, name, price, category="عام", barcode_text=None, stock_quantity=0):
        """إضافة منتج جديد"""
        try:
            # إنشاء باركود تلقائي إذا لم يتم توفيره
            if not barcode_text:
                barcode_text = self.generate_barcode()
            
            query = '''
                INSERT INTO products (name, barcode, price, category, stock_quantity)
                VALUES (?, ?, ?, ?, ?)
            '''
            product_id = self.db.execute_insert(query, (name, barcode_text, price, category, stock_quantity))
            
            # إنشاء صورة الباركود
            self.create_barcode_image(barcode_text, product_id)
            
            return product_id
        except Exception as e:
            print(f"خطأ في إضافة المنتج: {e}")
            return None
    
    def update_product(self, product_id, name=None, price=None, category=None, stock_quantity=None):
        """تحديث بيانات منتج"""
        try:
            # بناء الاستعلام ديناميكياً
            updates = []
            params = []
            
            if name:
                updates.append("name = ?")
                params.append(name)
            if price is not None:
                updates.append("price = ?")
                params.append(price)
            if category:
                updates.append("category = ?")
                params.append(category)
            if stock_quantity is not None:
                updates.append("stock_quantity = ?")
                params.append(stock_quantity)
            
            if not updates:
                return False
            
            params.append(product_id)
            query = f"UPDATE products SET {', '.join(updates)} WHERE id = ?"
            
            self.db.execute_query(query, params)
            return True
        except Exception as e:
            print(f"خطأ في تحديث المنتج: {e}")
            return False
    
    def delete_product(self, product_id):
        """حذف منتج"""
        try:
            query = "DELETE FROM products WHERE id = ?"
            self.db.execute_query(query, (product_id,))
            return True
        except Exception as e:
            print(f"خطأ في حذف المنتج: {e}")
            return False
    
    def get_product_by_id(self, product_id):
        """الحصول على منتج بالمعرف"""
        query = "SELECT * FROM products WHERE id = ?"
        result = self.db.execute_query(query, (product_id,))
        return result[0] if result else None
    
    def get_product_by_barcode(self, barcode_text):
        """الحصول على منتج بالباركود"""
        query = "SELECT * FROM products WHERE barcode = ?"
        result = self.db.execute_query(query, (barcode_text,))
        return result[0] if result else None
    
    def search_products(self, search_term):
        """البحث عن المنتجات"""
        query = '''
            SELECT * FROM products 
            WHERE name LIKE ? OR barcode LIKE ? OR category LIKE ?
            ORDER BY name
        '''
        search_pattern = f"%{search_term}%"
        return self.db.execute_query(query, (search_pattern, search_pattern, search_pattern))
    
    def get_all_products(self):
        """الحصول على جميع المنتجات"""
        query = "SELECT * FROM products ORDER BY name"
        return self.db.execute_query(query)
    
    def get_products_by_category(self, category):
        """الحصول على المنتجات حسب الفئة"""
        query = "SELECT * FROM products WHERE category = ? ORDER BY name"
        return self.db.execute_query(query, (category,))
    
    def get_low_stock_products(self, threshold=5):
        """الحصول على المنتجات منخفضة المخزون"""
        query = "SELECT * FROM products WHERE stock_quantity <= ? ORDER BY stock_quantity"
        return self.db.execute_query(query, (threshold,))
    
    def update_stock(self, product_id, quantity_change):
        """تحديث المخزون (إضافة أو خصم)"""
        try:
            query = "UPDATE products SET stock_quantity = stock_quantity + ? WHERE id = ?"
            self.db.execute_query(query, (quantity_change, product_id))
            return True
        except Exception as e:
            print(f"خطأ في تحديث المخزون: {e}")
            return False
    
    def generate_barcode(self):
        """إنشاء باركود تلقائي"""
        import time
        timestamp = str(int(time.time()))
        return f"PRD{timestamp[-8:]}"
    
    def create_barcode_image(self, barcode_text, product_id):
        """إنشاء صورة الباركود"""
        try:
            os.makedirs("data/barcodes", exist_ok=True)
            
            # إنشاء الباركود
            code128 = barcode.get_barcode_class('code128')
            barcode_instance = code128(barcode_text, writer=ImageWriter())
            
            # حفظ الصورة
            filename = f"data/barcodes/product_{product_id}"
            barcode_instance.save(filename)
            
            return f"{filename}.png"
        except Exception as e:
            print(f"خطأ في إنشاء صورة الباركود: {e}")
            return None
    
    def add_category(self, name, description=""):
        """إضافة فئة جديدة"""
        try:
            query = "INSERT INTO categories (name, description) VALUES (?, ?)"
            return self.db.execute_insert(query, (name, description))
        except Exception as e:
            print(f"خطأ في إضافة الفئة: {e}")
            return None
    
    def get_all_categories(self):
        """الحصول على جميع الفئات"""
        query = "SELECT * FROM categories ORDER BY name"
        return self.db.execute_query(query)
    
    def get_product_stats(self):
        """إحصائيات المنتجات"""
        stats = {}
        
        # إجمالي المنتجات
        total_query = "SELECT COUNT(*) FROM products"
        stats['total_products'] = self.db.execute_query(total_query)[0][0]
        
        # المنتجات حسب الفئة
        category_query = "SELECT category, COUNT(*) FROM products GROUP BY category"
        stats['by_category'] = self.db.execute_query(category_query)
        
        # إجمالي قيمة المخزون
        value_query = "SELECT SUM(price * stock_quantity) FROM products"
        result = self.db.execute_query(value_query)[0][0]
        stats['total_inventory_value'] = result if result else 0
        
        return stats

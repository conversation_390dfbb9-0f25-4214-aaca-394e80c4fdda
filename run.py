#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل برنامج الكاشير المتقدم
"""

import sys
import os
import subprocess

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("خطأ: يتطلب البرنامج Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_packages = [
        'tkinter',
        'sqlite3',
        'pandas',
        'openpyxl',
        'reportlab',
        'PIL',
        'matplotlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("المكتبات التالية غير مثبتة:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nيرجى تشغيل: python setup.py")
        return False
    
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة إذا لم تكن موجودة"""
    directories = [
        'data',
        'data/invoices',
        'data/reports',
        'data/barcodes'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)

def run_cashier_app():
    """تشغيل برنامج الكاشير"""
    try:
        # إنشاء المجلدات
        create_directories()
        
        # تشغيل البرنامج الرئيسي
        from main import main
        main()
        
    except ImportError as e:
        print(f"خطأ في استيراد الوحدات: {e}")
        print("يرجى التأكد من وجود جميع ملفات البرنامج")
        return False
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print("=== برنامج الكاشير المتقدم ===")
    print("جاري التحقق من المتطلبات...")
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من المكتبات
    if not check_dependencies():
        input("اضغط Enter للخروج...")
        return
    
    print("تم التحقق من جميع المتطلبات بنجاح!")
    print("جاري تشغيل البرنامج...")
    print("-" * 40)
    
    # تشغيل البرنامج
    if not run_cashier_app():
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()

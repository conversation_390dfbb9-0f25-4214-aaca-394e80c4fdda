from database import DatabaseManager
from product_manager import ProductManager
from datetime import datetime
import json

class POSSystem:
    def __init__(self):
        self.db = DatabaseManager()
        self.product_manager = ProductManager()
        self.current_cart = []
        self.current_total = 0.0
        self.invoice_number = self.generate_invoice_number()
    
    def generate_invoice_number(self):
        """إنشاء رقم فاتورة فريد"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"INV{timestamp}"
    
    def add_item_to_cart(self, product_id=None, barcode=None, quantity=1):
        """إضافة منتج إلى السلة"""
        try:
            # البحث عن المنتج
            if barcode:
                product = self.product_manager.get_product_by_barcode(barcode)
            elif product_id:
                product = self.product_manager.get_product_by_id(product_id)
            else:
                return False, "يجب توفير معرف المنتج أو الباركود"
            
            if not product:
                return False, "المنتج غير موجود"
            
            # التحقق من المخزون
            if product[5] < quantity:  # stock_quantity
                return False, f"المخزون غير كافي. المتوفر: {product[5]}"
            
            # البحث عن المنتج في السلة
            existing_item = None
            for item in self.current_cart:
                if item['product_id'] == product[0]:
                    existing_item = item
                    break
            
            if existing_item:
                # تحديث الكمية
                new_quantity = existing_item['quantity'] + quantity
                if product[5] < new_quantity:
                    return False, f"المخزون غير كافي. المتوفر: {product[5]}"
                
                existing_item['quantity'] = new_quantity
                existing_item['total_price'] = existing_item['quantity'] * existing_item['unit_price']
            else:
                # إضافة منتج جديد
                cart_item = {
                    'product_id': product[0],
                    'name': product[1],
                    'barcode': product[2],
                    'unit_price': product[3],
                    'quantity': quantity,
                    'total_price': product[3] * quantity
                }
                self.current_cart.append(cart_item)
            
            self.calculate_total()
            return True, "تم إضافة المنتج بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إضافة المنتج: {e}"
    
    def remove_item_from_cart(self, product_id):
        """إزالة منتج من السلة"""
        try:
            self.current_cart = [item for item in self.current_cart if item['product_id'] != product_id]
            self.calculate_total()
            return True, "تم حذف المنتج من السلة"
        except Exception as e:
            return False, f"خطأ في حذف المنتج: {e}"
    
    def update_item_quantity(self, product_id, new_quantity):
        """تحديث كمية منتج في السلة"""
        try:
            if new_quantity <= 0:
                return self.remove_item_from_cart(product_id)
            
            for item in self.current_cart:
                if item['product_id'] == product_id:
                    # التحقق من المخزون
                    product = self.product_manager.get_product_by_id(product_id)
                    if product[5] < new_quantity:
                        return False, f"المخزون غير كافي. المتوفر: {product[5]}"
                    
                    item['quantity'] = new_quantity
                    item['total_price'] = item['quantity'] * item['unit_price']
                    self.calculate_total()
                    return True, "تم تحديث الكمية"
            
            return False, "المنتج غير موجود في السلة"
            
        except Exception as e:
            return False, f"خطأ في تحديث الكمية: {e}"
    
    def calculate_total(self):
        """حساب المجموع الكلي"""
        self.current_total = sum(item['total_price'] for item in self.current_cart)
        return self.current_total
    
    def apply_discount(self, discount_amount=0, discount_percentage=0):
        """تطبيق خصم"""
        if discount_percentage > 0:
            discount_amount = self.current_total * (discount_percentage / 100)
        
        self.current_total = max(0, self.current_total - discount_amount)
        return self.current_total
    
    def process_payment(self, payment_method, amount_paid=None, cashier_name=""):
        """معالجة الدفع وإتمام البيع"""
        try:
            if not self.current_cart:
                return False, "السلة فارغة", None
            
            if payment_method == "نقدي" and amount_paid:
                if amount_paid < self.current_total:
                    return False, "المبلغ المدفوع أقل من المطلوب", None
                change = amount_paid - self.current_total
            else:
                change = 0
            
            # حفظ البيع في قاعدة البيانات
            sale_id = self.save_sale(payment_method, cashier_name)
            
            if sale_id:
                # تحديث المخزون
                self.update_inventory()
                
                # إنشاء بيانات الفاتورة
                invoice_data = {
                    'sale_id': sale_id,
                    'invoice_number': self.invoice_number,
                    'items': self.current_cart.copy(),
                    'total': self.current_total,
                    'payment_method': payment_method,
                    'amount_paid': amount_paid,
                    'change': change,
                    'date': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'cashier': cashier_name
                }
                
                # إعادة تعيين السلة
                self.clear_cart()
                
                return True, "تم إتمام البيع بنجاح", invoice_data
            else:
                return False, "خطأ في حفظ البيع", None
                
        except Exception as e:
            return False, f"خطأ في معالجة الدفع: {e}", None
    
    def save_sale(self, payment_method, cashier_name):
        """حفظ البيع في قاعدة البيانات"""
        try:
            # حفظ بيانات البيع الرئيسية
            sale_query = '''
                INSERT INTO sales (invoice_number, total_amount, payment_method, cashier_name)
                VALUES (?, ?, ?, ?)
            '''
            sale_id = self.db.execute_insert(sale_query, 
                (self.invoice_number, self.current_total, payment_method, cashier_name))
            
            # حفظ تفاصيل المنتجات
            for item in self.current_cart:
                item_query = '''
                    INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                '''
                self.db.execute_insert(item_query, (
                    sale_id,
                    item['product_id'],
                    item['quantity'],
                    item['unit_price'],
                    item['total_price']
                ))
            
            return sale_id
            
        except Exception as e:
            print(f"خطأ في حفظ البيع: {e}")
            return None
    
    def update_inventory(self):
        """تحديث المخزون بعد البيع"""
        for item in self.current_cart:
            self.product_manager.update_stock(item['product_id'], -item['quantity'])
    
    def clear_cart(self):
        """مسح السلة"""
        self.current_cart = []
        self.current_total = 0.0
        self.invoice_number = self.generate_invoice_number()
    
    def get_cart_summary(self):
        """ملخص السلة الحالية"""
        return {
            'items': self.current_cart,
            'total_items': len(self.current_cart),
            'total_quantity': sum(item['quantity'] for item in self.current_cart),
            'total_amount': self.current_total,
            'invoice_number': self.invoice_number
        }
    
    def search_product_for_sale(self, search_term):
        """البحث عن منتج للبيع"""
        return self.product_manager.search_products(search_term)
    
    def get_recent_sales(self, limit=10):
        """الحصول على المبيعات الأخيرة"""
        query = '''
            SELECT s.*, COUNT(si.id) as items_count
            FROM sales s
            LEFT JOIN sale_items si ON s.id = si.sale_id
            GROUP BY s.id
            ORDER BY s.sale_date DESC
            LIMIT ?
        '''
        return self.db.execute_query(query, (limit,))
    
    def get_sale_details(self, sale_id):
        """الحصول على تفاصيل بيع معين"""
        sale_query = "SELECT * FROM sales WHERE id = ?"
        sale = self.db.execute_query(sale_query, (sale_id,))
        
        if not sale:
            return None
        
        items_query = '''
            SELECT si.*, p.name, p.barcode
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            WHERE si.sale_id = ?
        '''
        items = self.db.execute_query(items_query, (sale_id,))
        
        return {
            'sale': sale[0],
            'items': items
        }
